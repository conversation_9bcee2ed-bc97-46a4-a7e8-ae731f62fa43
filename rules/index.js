const { config } = require('aws-sdk');
const { NudgeRule } = require('../src/nudgeEngine');
const daysToLive = config.nudgeTTLDays || 2;

class SleepMealTimingRule extends NudgeRule {
  constructor(config = {}) {
    super("sleep_meal_gap", "Encourage early dinner before sleep.");
    this.minGapHours = config.params?.minGapHours || 4;
    this.priority = config.priority || "medium";
    this.ttl = config.ttl || 86400;
  }

  applies({ data }) {
    const lastMealTime = data.mealLogs?.date || null;
    const sleepTime = data.sleepLogs?.startTime || null;
    if (!lastMealTime || !sleepTime) return false;
    const gap = (new Date(sleepTime) - new Date(lastMealTime)) / (1000 * 60 * 60);
    return (gap > 0) && (gap < this.minGapHours);
  }

  generateNudge(data) {
    const userId = data.userId;
    const category = "steps";
    const title = "Too Close to Bedtime?";
    const message = `Try to finish your last meal at least ${this.minGapHours} hours before bedtime.`;
    return getNudgeDocument(userId, category, title, message, this.priority, this.ttl);
  }
}

class LateMealNightRule extends NudgeRule {
  constructor(config = {}) {
    super("late_meal_night", "Detect late-night eating patterns.");
    this.lateHour = config.params?.lateHour || 22;
    this.daysThreshold = config.params?.daysThreshold || 2;
    this.priority = config.priority || "medium";
    this.ttl = config.ttl || 86400;
  }

  applies({ data }) {
    const recentMealLogs = data.recentMealLogs || [];
    if (!recentMealLogs || recentMealLogs.length === 0) return false;

    // Group meals by date and check for late meals
    const lateMealDays = new Set();

    recentMealLogs.forEach(meal => {
      const mealDate = new Date(meal.date);
      const mealHour = mealDate.getHours();

      // Check if meal was logged after the late hour threshold
      if (mealHour >= this.lateHour) {
        const dateKey = mealDate.toDateString();
        lateMealDays.add(dateKey);
      }
    });

    // Check if we have late meals on threshold or more days
    return lateMealDays.size >= this.daysThreshold;
  }

  generateNudge(data) {
    const userId = data.userId;
    const category = "nutrition";
    const title = "Late Night Eating Pattern";
    const message = `You've been logging late-night meals past ${this.lateHour}:00 on ${this.daysThreshold} or more days. Consider lighter meals earlier in the evening.`;
    return getNudgeDocument(userId, category, title, message, this.priority, this.ttl);
  }
}

function getNudgeDocument(userId, category, title, message, priority = "medium", ttlSeconds = null) {
  const currentTime = new Date();
  const ttlMilliseconds = ttlSeconds ? ttlSeconds * 1000 : daysToLive * 24 * 60 * 60 * 1000;
  const ttlDate = new Date(currentTime.getTime() + ttlMilliseconds);

  const doc = {
    userId,
    type: "nudge",
    content: {
      title,
      message,
    },
    timestamp: currentTime.toISOString(),
    category,
    priority,
    ttl: ttlDate.toISOString(),
    status: "open"
  };

  return doc;
}

module.exports = {
  SleepMealTimingRule,
  LateMealNightRule,
};