const { MealLogsModel } = require("../models/MealLogs");

const getUserMealLogsByDate = async (userId, startDate, endDate) => {
  return await MealLogsModel.find({
    userId,
    date: { $gte: startDate, $lte: endDate }
  }).sort({ date: 'desc' }).lean();
};

const getMealLogsByPagination = async (userId, pagination = true, pageNumber = 1, limit = 20) => {
  let skip = 0;
  if (pagination) skip = (pageNumber - 1) * limit;

  return await MealLogsModel.find({ userId }).sort({ date: "desc" }).skip(skip).limit(limit).lean();
};

const getLastMealLog = async (userId) => {
  const mealLogs = await getMealLogsByPagination(userId, true, 1, 1);
  return mealLogs?.[0] || null;
};

const getRecentMealLogs = async (userId, days = 7) => {
  const endDate = new Date();
  const startDate = new Date();
  startDate.setDate(endDate.getDate() - days);

  return await getUserMealLogsByDate(userId, startDate, endDate);
};

module.exports = {
  getUserMealLogsByDate,
  getLastMealLog,
  getRecentMealLogs,
};
