const { getOSClient } = require("../utils/connection");
const targetService = require("./targets");
const { getTrackerIdsByCategories } = require("./trackers");
const { getAllLogsByDateRange, trackerMap, MANUAL_ENTRY_DEVICE_ID, getLastLogByDeviceId } = require("./logs");
const { getUserMealLogsByDate: getMealLogs } = require("./meallogs");
const { getStaticTargetsMap } = require("../utils/staticData");
const { roundOffNumber, getTimeDiffInMin } = require("../utils/helpers");
const { config } = require("../environment/index");
const { log: logger } = require("../utils/logger");
const indexName = config.INDEX.targets_achieved;
const exerciseTargetIds = config.exerciseTargetIds;
const mindfulnessTargetIds = config.mindfulnessTargetIds;

const trackerCategories = {
  nutrition: "Nutrition",
  activity: "Activity",
  sleep: "Sleep",
  vital: "Vitals",
  bodyMeasurement: "Body Measurements",
  mindfulness: "Mindfulness"
};

async function insertTargetAchieved(document) {
  const client = getOSClient();
  try {
    const response = await client.index({
      index: indexName,
      body: document,
    });
    return response.body?._id;
  } catch (error) {
    logger.warn('Failed to insert Log', JSON.stringify(error));
    return null;
  }
}

async function upsertTargetAchieved(userId, targetId, date, doc) {
  const client = getOSClient();
  const response = await client.search({
    index: indexName,
    body: {
      query: {
        bool: {
          must: [
            { match: { "userId.keyword": userId } },
            { match: { targetId } },
            { match: { date } },
          ],
        },
      },
      size: 1
    },
  });

  const existingTargetAchieved =  response.body?.hits?.hits?.[0] || null

  if (existingTargetAchieved) {
    await client.update({
      index: indexName, id: existingTargetAchieved._id,
      body: { doc }
    });
    return existingTargetAchieved._id;
  } else {
    await insertTargetAchieved(doc);
    await client.indices.refresh({ index: indexName });
  }  
}

async function computeTargetAchievement(userId, trackerId, deviceId, date, startTime, endTime) {
  const targetsMap = await getStaticTargetsMap();
  const targetIds = getAllTargets(trackerId, targetsMap)

  let deviceIds = [deviceId];
  const vitalAndBodyMeasurementTrackerIds = await getTrackerIdsByCategories([trackerCategories.vital, trackerCategories.bodyMeasurement]);
  if (vitalAndBodyMeasurementTrackerIds.includes(Number(trackerId)) && (Number(deviceId) != MANUAL_ENTRY_DEVICE_ID)) {
    deviceIds.push(MANUAL_ENTRY_DEVICE_ID);
  }
  deviceIds = deviceIds.filter((id) => id != null);
  const targetAchievementDocs = {};
  for (const targetId of targetIds) {
    let target = await targetService.getLatestTargetById(userId, targetId)
    logger.info(`target set: ${JSON.stringify(target)}`);
    // Deliberately calculating to get whole day data even if target is inactive or not
    if (!target || !target.isActive) { 
      target = {
        targetId: Number(targetId),
        value: null
      }
    }
    const targetDetails = targetsMap[targetId]
    let trackerIndexName = trackerMap[trackerId].indexName;
    let filterByDeviceId = true;
    if (exerciseTargetIds.includes(targetId)) {
      trackerIndexName = config.INDEX.exerciseLogs;
      filterByDeviceId = false;
    } else if (mindfulnessTargetIds.includes(targetId)) {
      trackerIndexName = config.INDEX.mindfulnessLogs;
      filterByDeviceId = false;
    }
    let value = 0, logIds = [];
    switch (targetId) {
      case "1": 
      case "3":
      case "4":
      case "5": 
      case "6":
      case "7":
      case "8":
      case "9":
      case "10":
      case "11":
      case "12":
      case "13":
      case "14":
      case "29":
      case "33":
      case "34":
      case "35":
      case "37": { 
        const logs = await getAllLogsByDateRange(userId, trackerIndexName, startTime, endTime, filterByDeviceId, deviceIds);
        value = getSumOfLogValues(targetId, logs);
        logIds = logs.map(log => log?.id || null);
        break;
      }
      case "16":
      case "18":
      case "20":
      case "21":
      case "23":
      case "24":
      case "25":
      case "26":
      case "27": 
      case "28":
      case "39":
      case "40":
      case "41":
      case "42":
      case "43":
      case "44": {
        const log = await getLastLogByDeviceId(userId, trackerIndexName, deviceIds,  startTime, endTime);
        logger.info(`lastLog: ${JSON.stringify(log)}`);
        value = getLogValue(targetId, log);
        logIds = log?.id ? [log.id] : [];
        break;
      }
      case "19": {  // Blood Pressure
        const log = await getLastLogByDeviceId(userId, trackerIndexName, deviceIds, startTime, endTime);
        logIds = log?.id ? [log.id] : [];
        let isMet = false;
        const targetAchieved = {
          userId, targetId: Number(targetId), date,
          valueDiastole: log.diastole, valueSystole: log.systole,  
          targetDiastole: target?.diastole || null, targetSystole: target?.systole || null,  
          percentage: 0, isMet, logIds,
        }
        if(target?.diastole && target?.systole) {
          if (target.diastole <= log.diastole && target.systole >= log.systole) {
            isMet = true;
          }
          const diastoleDeviation = Math.abs(target.diastole - log.diastole);
          const systoleDeviation = Math.abs(target.systole - log.systole);
          const achievementPercentageDiastole = (1 - diastoleDeviation / target.diastole) * 100;
          const achievementPercentageSystole = (1 - systoleDeviation / target.systole) * 100;
          const percentage = roundOffNumber((achievementPercentageDiastole + achievementPercentageSystole) / 2, 2);
          
          targetAchieved.percentage = percentage;
          targetAchieved.isMet = isMet;
        }
        const docId = await upsertTargetAchieved(userId, targetId, date, targetAchieved)  
        targetAchievementDocs[targetId] = docId;     
        continue; 
      }
      case "17":    // resting heart rate
      case "22": {  // heart rate variability
        const logs = await getAllLogsByDateRange(userId, trackerIndexName, startTime, endTime, filterByDeviceId, deviceIds);
        logger.info(`hrvLogs: ${JSON.stringify(logs)}`);
        value = getAvgValue(targetId, logs);
        logIds = logs.map(log => log?.id || null);
        break;
      }
      case "2": {
        const startTime = new Date(0).toISOString();
        const waistLog = await getLastLogByDeviceId(userId, trackerMap[14].indexName, deviceIds,  startTime, endTime);
        const heightLog = await getLastLogByDeviceId(userId, trackerMap[19].indexName, deviceIds, startTime, endTime);
        value = heightLog?.value ? roundOffNumber(waistLog?.value / heightLog.value) : null;
        logIds = (waistLog?.id && heightLog?.id) ? [waistLog.id, heightLog.id] : [];
        break;
      }
      case "15": 
      case "30": 
      case "31": 
      case "32": 
      case "36": {
        const mealLogs = await getMealLogs(userId, new Date(startTime), new Date(endTime));
        value = getSumOfLogValues(targetId, mealLogs);
        logIds = mealLogs.map(log => log?.id || null);
        break;
      } 
      case "38": {
        const mealLogsForDay = await getMealLogs(userId, new Date(startTime), new Date(endTime));
        const firstMealLogTimestamp = mealLogsForDay?.[0]?.date || null;
        const lastMealLogTimestamp = mealLogsForDay?.pop()?.date || null;
        if(!firstMealLogTimestamp && !lastMealLogTimestamp) {
          return 0;
        }
        const totalFastingHours = 24 - (getTimeDiffInMin(firstMealLogTimestamp, lastMealLogTimestamp) / 60);
        return roundOffNumber(totalFastingHours, 2);
      }
      default: {
          targetAchievementDocs[targetId] = null;
          continue;
      }
    }

    const targetAchievedDoc = getTargetAchievedDoc(userId, target, targetDetails, date, value, logIds);
    const docId = await upsertTargetAchieved(userId, targetId, date, targetAchievedDoc)
    targetAchievementDocs[targetId] = docId;
  }
  return targetAchievementDocs;
}

function getLogValue(targetId, log) {
  if(!log) return 0;
  try {
    // All the time values will be in seconds in logs as well as in target setting
    switch(targetId) {
      case "1": return log?.value || 0;
      // case "2": Waist to Height ratio, handled seperately
      case "3": return log?.steps || 0;
      case "4": return log?.duration || 0; // sleep duration
      case "5": return log?.activityTime?.totalActiveSeconds || 0;
      case "6": return log?.activityCalories?.activeCalories || 0;
      case "7": return log?.activityCalories?.bmrCalories || 0;
      case "8": return log?.standMinutes || 0;
      case "9": return log?.standHours || 0;
      case "10": return log?.isComplete ? (log?.details?.totalDuration || 0) : 0;
      case "11": return log?.isComplete ? (log?.details?.totalDuration || 0) : 0;
      case "12": return log?.levels?.summary?.deep?.seconds || 0;
      case "13": return log?.floors || 0;
      case "14": return log?.distance || 0;
      case "15": {
        const mealTags = log.mealTags;
        const totalCalories = mealTags.reduce((total, item) => { return ( total + Number(item?.computedNutritions?.calories?.quantity || 0)); }, 0);
        return totalCalories;
      }
      case "16": return log?.value || 0;
      case "17": return log?.value || 0; // resting heart rate
      case "18": return log?.value || 0;
      case "19": return log?.diastole || 0;
      case "20": return log?.value || 0;
      case "21": return log?.avg || log?.value || 0;
      case "22": return log?.dailyRmssd || 0;// heart rate variability
      case "23": return log?.value || 0;
      case "24": return log?.value || 0;
      case "25": return log?.value || 0;
      case "26": return log?.value || 0;
      case "27": return log?.value || 0;
      case "28": return log?.value || 0;
      case "29": return log?.isComplete ? (log?.details?.totalScore || 0) : 0;
      case "30": { 
        const mealTags = log.mealTags;
        const totalCarbs = mealTags.reduce((total, item) => { return ( total + Number(item?.computedNutritions?.carbohydrates?.quantity || 0)); }, 0);
        return totalCarbs;
      }
      case "31": {
        const mealTags = log.mealTags;
        const totalFats = mealTags.reduce((total, item) => { return ( total + Number(item?.computedNutritions?.fat?.quantity || 0)); }, 0);
        return totalFats;
      }
      case "32": {
        const mealTags = log.mealTags;
        const totalProtein = mealTags.reduce((total, item) => { return ( total + Number(item?.computedNutritions?.protein?.quantity || 0)); }, 0);
        return totalProtein;
      }
      case "33": return null; // Exercise count target, will be handled in different ticket
      case "34": return log?.isComplete ? (log?.details?.totalScore || 0) : 0;
      case "35": return null; // Mindfulness count target, will be handled in different ticket
      case "36": {
        const mealTags = log.mealTags;
        const totalFiber = mealTags.reduce((total, item) => { return ( total + Number(item?.computedNutritions?.carbohydrates?.constituents?.dietryFiber?.quantity || 0)); }, 0);
        return totalFiber;
      }
      case "37": return log?.activityCalories?.totalCalories || 0
      // case "38": Fasting target, handled seperately
      case "39": return log?.value || 0;
      // Targets 40 to 44 are dummy targets used to get last day data
      case "40": return log?.value || 0;
      case "41": return log?.intraday?.at(-1)?.value || 0;
      case "42": return log?.value || 0;
      case "43": return log?.value || 0;
      case "44": return log?.value || 0;
    }
  } catch {}
  return 0
}

function getSumOfLogValues(targetId, logs) {
  let totalValue = 0;
  logs.forEach((log) => {
    totalValue += getLogValue(targetId, log) || 0;
  });
  return totalValue;
}

function getAvgValue(targetId, logs) {
  let totalValue = getSumOfLogValues(targetId, logs);
  let noOfLogs = logs.length;
  const avgValue = roundOffNumber(totalValue / noOfLogs, 2);
  return avgValue;
}

function getAllTargets(givenTrackerId, targetsMap) {
  givenTrackerId = parseInt(givenTrackerId)
  return Object.keys(targetsMap).filter(targetId =>
    targetsMap[targetId].trackerIds.includes(givenTrackerId)
  ) || []
}

function getTargetAchievedDoc(userId, target, targetDetails, date, value, logIds){
  const targetId = target.targetId;
  let isMet = false
  if (target.value == null) { // For inactive targets
    const targetAchieved = {
      userId,
      targetId: Number(targetId),
      date,
      value: roundOffNumber(value),
      target: null,
      percentage: 0,
      isMet,
      logIds
    };
    return targetAchieved;
  }
  if (targetDetails.favorability == "Positive") {
    isMet = value >= target.value * (1 - (target?.threshold || 0) / 100);
  } else {
    // For Negative favorability targets, isMet will be calculated based on range values
    const lowerBound = target.value * (1 - (target?.threshold || 0) / 100);
    const upperBound = target.value * (1 + (target?.threshold || 0) / 100);
    isMet = ((value >= lowerBound) && (value <= upperBound));
  }

  let percentage = isMet ? 100 : 0;
  if (targetDetails.type == "percentage") {
    percentage = (value / target.value) * 100;
    if (percentage > 100) percentage = 100;
    if (percentage < 0) percentage = 0;
  }

  const targetAchieved = {
    userId, targetId: Number(targetId),
    date,
    value: roundOffNumber(value),
    target: Number(target.value),
    percentage: roundOffNumber(percentage), isMet, logIds
  }
  return targetAchieved;
}

module.exports = {
  computeTargetAchievement,
}
